# Server Configuration
server.port=8080
spring.application.name=mcp-server

# Database Configuration (PostgreSQL for production, H2 for testing)
spring.datasource.url=${DATABASE_URL:jdbc:h2:mem:testdb}
spring.datasource.username=${DATABASE_USERNAME:sa}
spring.datasource.password=${DATABASE_PASSWORD:}
spring.datasource.driver-class-name=${DATABASE_DRIVER:org.h2.Driver}

# JPA Configuration
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=${HIBERNATE_DIALECT:org.hibernate.dialect.H2Dialect}
spring.jpa.properties.hibernate.format_sql=true

# Flyway Configuration (disabled for testing with H2)
spring.flyway.enabled=false
# spring.flyway.locations=classpath:db/migration
# spring.flyway.baseline-on-migrate=true

# H2 Console (for testing only)
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# Kylas API Configuration
kylas.api.url=https://api.kylas.io/v1/leads
kylas.api.key=${KYLAS_API_KEY:your-api-key-here}

# Logging Configuration
logging.level.com.sell.mcp=INFO
logging.level.org.springframework.ai.mcp=DEBUG
logging.level.org.springframework.web=INFO

# Security Configuration (OAuth2 disabled for testing)
# spring.security.oauth2.resourceserver.jwt.issuer-uri=${OAUTH2_ISSUER_URI:}

# MCP Server Configuration
spring.ai.mcp.server.enabled=true

spring.ai.openai.api-key=********************************************************************************************************************************************************************