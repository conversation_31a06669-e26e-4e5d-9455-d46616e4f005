# Server Configuration
server.port=8080
spring.application.name=mcp-server

# Database Configuration
spring.datasource.url=**************************************
spring.datasource.username=mcpuser
spring.datasource.password=mcppass
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=true

# Flyway Configuration
spring.flyway.enabled=true
spring.flyway.locations=classpath:db/migration
spring.flyway.baseline-on-migrate=true

# Kylas API Configuration
kylas.api.url=https://api.kylas.io/v1/leads
kylas.api.key=${KYLAS_API_KEY:your-api-key-here}

# Logging Configuration
logging.level.com.sell.mcp=INFO
logging.level.org.springframework.ai.mcp=DEBUG
logging.level.org.springframework.web=INFO

# Security Configuration (for OAuth2 if needed)
spring.security.oauth2.resourceserver.jwt.issuer-uri=${OAUTH2_ISSUER_URI:}

# MCP Server Configuration
spring.ai.mcp.server.enabled=true
