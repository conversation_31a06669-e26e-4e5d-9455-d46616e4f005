<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="b2deb379-12fb-41b6-a1f7-8cf5f9fa5310" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHomeTypeForPersistence" value="WRAPPER" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 2
}]]></component>
  <component name="ProjectId" id="3231Lmxw6luuMc6fNXljJy5b3Es" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.zencoder-patch-add-chat-message-raw-content": "true",
    "RunOnceActivity.zencoder-patch-chat-storage-migration": "true",
    "RunOnceActivity.zencoder-patch-generate-chat-message-id": "true",
    "RunOnceActivity.zencoder-patch-migrate-custom-instruction": "true",
    "RunOnceActivity.zencoder-patch-migrate-repo-info-to-rules": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "/home/<USER>/repo/sd-mcp2"
  }
}]]></component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="b2deb379-12fb-41b6-a1f7-8cf5f9fa5310" name="Changes" comment="" />
      <created>1756634388926</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1756634388926</updated>
    </task>
    <servers />
  </component>
  <component name="ai.zencoder.plugin.mcp">
    <option name="internalToolsState" value="{&quot;file_search&quot;:true,&quot;list_resources&quot;:true,&quot;fulltext_search&quot;:true,&quot;read_resource&quot;:true}" />
  </component>
</project>