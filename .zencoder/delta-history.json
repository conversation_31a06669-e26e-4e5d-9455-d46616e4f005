{"snapshots": {"/home/<USER>/repo/sd-mcp2/src/main/resources/application.properties": {"filePath": "/home/<USER>/repo/sd-mcp2/src/main/resources/application.properties", "baseContent": "# Server Configuration\nserver.port=8080\nspring.application.name=mcp-server\n\n# Database Configuration\nspring.datasource.url=*****************************************************************************************************************************************************************************# JPA Configuration\nspring.jpa.hibernate.ddl-auto=validate\nspring.jpa.show-sql=false\nspring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect\nspring.jpa.properties.hibernate.format_sql=true\n\n# Flyway Configuration\nspring.flyway.enabled=true\nspring.flyway.locations=classpath:db/migration\nspring.flyway.baseline-on-migrate=true\n\n# Kylas API Configuration\nkylas.api.url=https://api.kylas.io/v1/leads\nkylas.api.key=${KYLAS_API_KEY:your-api-key-here}\n\n# Logging Configuration\nlogging.level.com.sell.mcp=INFO\nlogging.level.org.springframework.ai.mcp=DEBUG\nlogging.level.org.springframework.web=INFO\n\n# Security Configuration (for OAuth2 if needed)\nspring.security.oauth2.resourceserver.jwt.issuer-uri=${OAUTH2_ISSUER_URI:}\n\n# MCP Server Configuration\nspring.ai.mcp.server.enabled=true\n\nspring.ai.openai.api-key=", "baseTimestamp": 1756634609395, "deltas": [{"timestamp": 1756634650326, "changes": [{"type": "MODIFY", "lineNumber": 36, "content": "spring.ai.openai.api-key=********************************************************************************************************************************************************************", "oldContent": "spring.ai.openai.api-key="}]}, {"timestamp": 1756635159251, "changes": [{"type": "DELETE", "lineNumber": 30, "oldContent": "# Security Configuration (for OAuth2 if needed)"}, {"type": "DELETE", "lineNumber": 31, "oldContent": "spring.security.oauth2.resourceserver.jwt.issuer-uri=${OAUTH2_ISSUER_URI:}"}, {"type": "INSERT", "lineNumber": 30, "content": "# Security Configuration (OAuth2 disabled for testing)"}, {"type": "INSERT", "lineNumber": 31, "content": "# spring.security.oauth2.resourceserver.jwt.issuer-uri=${OAUTH2_ISSUER_URI:}"}]}, {"timestamp": 1756635174269, "changes": [{"type": "DELETE", "lineNumber": 4, "oldContent": "# Database Configuration"}, {"type": "DELETE", "lineNumber": 5, "oldContent": "spring.datasource.url=**************************************"}, {"type": "DELETE", "lineNumber": 6, "oldContent": "spring.datasource.username=mcpuser"}, {"type": "DELETE", "lineNumber": 7, "oldContent": "spring.datasource.password=mcppass"}, {"type": "DELETE", "lineNumber": 8, "oldContent": "spring.datasource.driver-class-name=org.postgresql.Driver"}, {"type": "INSERT", "lineNumber": 4, "content": "# Database Configuration (PostgreSQL for production, H2 for testing)"}, {"type": "INSERT", "lineNumber": 5, "content": "spring.datasource.url=${DATABASE_URL:jdbc:h2:mem:testdb}"}, {"type": "INSERT", "lineNumber": 6, "content": "spring.datasource.username=${DATABASE_USERNAME:sa}"}, {"type": "INSERT", "lineNumber": 7, "content": "spring.datasource.password=${DATABASE_PASSWORD:}"}, {"type": "INSERT", "lineNumber": 8, "content": "spring.datasource.driver-class-name=${DATABASE_DRIVER:org.h2.Driver}"}, {"type": "MODIFY", "lineNumber": 11, "content": "spring.jpa.hibernate.ddl-auto=create-drop", "oldContent": "spring.jpa.hibernate.ddl-auto=validate"}, {"type": "MODIFY", "lineNumber": 13, "content": "spring.jpa.properties.hibernate.dialect=${HIBERNATE_DIALECT:org.hibernate.dialect.H2Dialect}", "oldContent": "spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect"}, {"type": "DELETE", "lineNumber": 16, "oldContent": "# Flyway Configuration"}, {"type": "DELETE", "lineNumber": 17, "oldContent": "spring.flyway.enabled=true"}, {"type": "DELETE", "lineNumber": 18, "oldContent": "spring.flyway.locations=classpath:db/migration"}, {"type": "DELETE", "lineNumber": 19, "oldContent": "spring.flyway.baseline-on-migrate=true"}, {"type": "INSERT", "lineNumber": 16, "content": "# Flyway Configuration (disabled for testing with H2)"}, {"type": "INSERT", "lineNumber": 17, "content": "spring.flyway.enabled=false"}, {"type": "INSERT", "lineNumber": 18, "content": "# spring.flyway.locations=classpath:db/migration"}, {"type": "INSERT", "lineNumber": 19, "content": "# spring.flyway.baseline-on-migrate=true"}, {"type": "INSERT", "lineNumber": 21, "content": "# H2 Console (for testing only)"}, {"type": "INSERT", "lineNumber": 22, "content": "spring.h2.console.enabled=true"}, {"type": "INSERT", "lineNumber": 23, "content": "spring.h2.console.path=/h2-console"}, {"type": "INSERT", "lineNumber": 24, "content": ""}]}]}, "/home/<USER>/repo/sd-mcp2/README.md": {"filePath": "/home/<USER>/repo/sd-mcp2/README.md", "baseContent": "# Spring AI MCP Server for Lead Creation\n\nThis is a Spring AI MCP (Model Context Protocol) Server that provides a tool for creating leads in Kylas CRM.\n\n## Features\n\n- **Lead Creation Tool**: Creates leads in Kylas CRM with firstName and lastName\n- **MCP Protocol**: Implements Model Context Protocol for AI integration\n- **OAuth2 Ready**: Configured for OAuth2 security (optional)\n- **Docker Support**: Includes Dockerfile for containerization\n\n## Prerequisites\n\n- Java 17 or higher\n- Maven 3.6+ (or use included <PERSON><PERSON> wrapper)\n- PostgreSQL database (for production)\n- Kylas API key\n\n## Configuration\n\n### Environment Variables\n\nSet the following environment variables:\n\n```bash\nexport KYLAS_API_KEY=your-kylas-api-key-here\nexport SPRING_DATASOURCE_URL=**************************************\nexport SPRING_DATASOURCE_USERNAME=mcpuser\nexport SPRING_DATASOURCE_PASSWORD=mcppass\n```\n\n### Application Properties\n\nKey configuration in `src/main/resources/application.properties`:\n\n- `kylas.api.url`: Kylas API endpoint (default: https://api.kylas.io/v1/leads)\n- `kylas.api.key`: Your Kylas API key\n- Database connection settings\n\n## Running Locally (Simplified for Testing)\n\n### 1. Quick Start (No Database Required)\n\nThe application now uses H2 in-memory database by default for easy testing:\n\n```bash\n# Run with minimal configuration\nKYLAS_API_KEY=your-key ./mvnw spring-boot:run\n```\n\n### 2. With PostgreSQL (Production Setup)\n\n```bash\n# Start PostgreSQL\ndocker-compose up -d postgres\n\n# Run with PostgreSQL\nDATABASE_URL=************************************** \\\nDATABASE_USERNAME=mcpuser \\\nDATABASE_PASSWORD=mcppass \\\nDATABASE_DRIVER=org.postgresql.Driver \\\nHIBERNATE_DIALECT=org.hibernate.dialect.PostgreSQLDialect \\\nKYLAS_API_KEY=your-key \\\n./mvnw spring-boot:run\n```\n\n### 3. Test the Application\n\nCheck health endpoint:\n```bash\ncurl http://localhost:8080/health\n```\n\n## MCP Tool Usage\n\nThe server exposes one MCP tool:\n\n### create_lead\n\nCreates a new lead in Kylas CRM.\n\n**Parameters:**\n- `firstName` (string, required): First name of the lead\n- `lastName` (string, required): Last name of the lead\n\n**Example MCP Tool Call:**\n```json\n{\n  \"tool\": \"create_lead\",\n  \"arguments\": {\n    \"firstName\": \"John\",\n    \"lastName\": \"Doe\"\n  }\n}\n```\n\n## Docker Deployment\n\n### Build Docker Image\n\n```bash\ndocker build -t mcp-server .\n```\n\n### Run with Docker\n\n```bash\ndocker run -p 8080:8080 \\\n  -e KYLAS_API_KEY=your-key \\\n  -e SPRING_DATASOURCE_URL=************************************************* \\\n  mcp-server\n```\n\n## Free Hosting Deployment Options\n\n### 1. Railway (Recommended)\n\n1. Create account at [Railway.app](https://railway.app)\n2. Connect your GitHub repository\n3. Add environment variables in Railway dashboard:\n   - `KYLAS_API_KEY`\n   - `SPRING_DATASOURCE_URL` (Railway provides PostgreSQL)\n4. Deploy automatically from GitHub\n\n### 2. Render\n\n1. Create account at [Render.com](https://render.com)\n2. Create new Web Service from GitHub repo\n3. Set build command: `./mvnw clean package -DskipTests`\n4. Set start command: `java -jar target/*.jar`\n5. Add environment variables in Render dashboard\n\n### 3. Heroku\n\n1. Install Heroku CLI\n2. Create Heroku app:\n   ```bash\n   heroku create your-mcp-server\n   heroku addons:create heroku-postgresql:mini\n   heroku config:set KYLAS_API_KEY=your-key\n   git push heroku main\n   ```\n\n### 4. Google Cloud Run\n\n1. Build and push to Google Container Registry:\n   ```bash\n   gcloud builds submit --tag gcr.io/PROJECT-ID/mcp-server\n   ```\n2. Deploy to Cloud Run:\n   ```bash\n   gcloud run deploy --image gcr.io/PROJECT-ID/mcp-server --platform managed\n   ```\n\n## Testing the Deployment\n\nOnce deployed, test your MCP server:\n\n1. **Health Check:**\n   ```bash\n   curl https://your-app-url/health\n   ```\n\n2. **MCP Endpoint:**\n   ```bash\n   curl https://your-app-url/mcp\n   ```\n\n## Environment Variables for Production\n\n```bash\nKYLAS_API_KEY=your-actual-kylas-api-key\nSPRING_DATASOURCE_URL=your-database-url\nSPRING_DATASOURCE_USERNAME=your-db-username\nSPRING_DATASOURCE_PASSWORD=your-db-password\nSPRING_PROFILES_ACTIVE=prod\n```\n\n## Security Notes\n\n- The application is configured for OAuth2 Resource Server (optional)\n- Set `OAUTH2_ISSUER_URI` if using OAuth2 authentication\n- Use HTTPS in production\n- Keep your Kylas API key secure\n\n## Troubleshooting\n\n1. **Database Connection Issues**: Ensure PostgreSQL is running and credentials are correct\n2. **Kylas API Issues**: Verify your API key and network connectivity\n3. **Port Conflicts**: Change `server.port` in application.properties if needed\n\n## API Documentation\n\n- Health: `GET /health`\n- Root: `GET /`\n- MCP Endpoint: `GET /mcp` (Server-Sent Events)\n", "baseTimestamp": 1756635204376}}}