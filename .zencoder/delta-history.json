{"snapshots": {"/home/<USER>/repo/sd-mcp2/src/main/resources/application.properties": {"filePath": "/home/<USER>/repo/sd-mcp2/src/main/resources/application.properties", "baseContent": "# Server Configuration\nserver.port=8080\nspring.application.name=mcp-server\n\n# Database Configuration\nspring.datasource.url=*****************************************************************************************************************************************************************************# JPA Configuration\nspring.jpa.hibernate.ddl-auto=validate\nspring.jpa.show-sql=false\nspring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect\nspring.jpa.properties.hibernate.format_sql=true\n\n# Flyway Configuration\nspring.flyway.enabled=true\nspring.flyway.locations=classpath:db/migration\nspring.flyway.baseline-on-migrate=true\n\n# Kylas API Configuration\nkylas.api.url=https://api.kylas.io/v1/leads\nkylas.api.key=${KYLAS_API_KEY:your-api-key-here}\n\n# Logging Configuration\nlogging.level.com.sell.mcp=INFO\nlogging.level.org.springframework.ai.mcp=DEBUG\nlogging.level.org.springframework.web=INFO\n\n# Security Configuration (for OAuth2 if needed)\nspring.security.oauth2.resourceserver.jwt.issuer-uri=${OAUTH2_ISSUER_URI:}\n\n# MCP Server Configuration\nspring.ai.mcp.server.enabled=true\n\nspring.ai.openai.api-key=", "baseTimestamp": 1756634609395, "deltas": [{"timestamp": 1756634650326, "changes": [{"type": "MODIFY", "lineNumber": 36, "content": "spring.ai.openai.api-key=********************************************************************************************************************************************************************", "oldContent": "spring.ai.openai.api-key="}]}]}}}