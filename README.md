# Spring AI MCP Server for Lead Creation

This is a Spring AI MCP (Model Context Protocol) Server that provides a tool for creating leads in Kylas CRM.

## Features

- **Lead Creation Tool**: Creates leads in Kylas CRM with firstName and lastName
- **MCP Protocol**: Implements Model Context Protocol for AI integration
- **OAuth2 Ready**: Configured for OAuth2 security (optional)
- **Docker Support**: Includes Dockerfile for containerization

## Prerequisites

- Java 17 or higher
- Maven 3.6+ (or use included <PERSON><PERSON> wrapper)
- PostgreSQL database (for production)
- Kylas API key

## Configuration

### Environment Variables

Set the following environment variables:

```bash
export KYLAS_API_KEY=your-kylas-api-key-here
export SPRING_DATASOURCE_URL=**************************************
export SPRING_DATASOURCE_USERNAME=mcpuser
export SPRING_DATASOURCE_PASSWORD=mcppass
```

### Application Properties

Key configuration in `src/main/resources/application.properties`:

- `kylas.api.url`: Kylas API endpoint (default: https://api.kylas.io/v1/leads)
- `kylas.api.key`: Your Kylas API key
- Database connection settings

## Running Locally

### 1. Start PostgreSQL Database

```bash
docker-compose up -d postgres
```

### 2. Run the Application

```bash
./mvnw spring-boot:run
```

Or with environment variables:

```bash
KYLAS_API_KEY=your-key ./mvnw spring-boot:run
```

### 3. Test the Application

Check health endpoint:
```bash
curl http://localhost:8080/health
```

## MCP Tool Usage

The server exposes one MCP tool:

### create_lead

Creates a new lead in Kylas CRM.

**Parameters:**
- `firstName` (string, required): First name of the lead
- `lastName` (string, required): Last name of the lead

**Example MCP Tool Call:**
```json
{
  "tool": "create_lead",
  "arguments": {
    "firstName": "John",
    "lastName": "Doe"
  }
}
```

## Docker Deployment

### Build Docker Image

```bash
docker build -t mcp-server .
```

### Run with Docker

```bash
docker run -p 8080:8080 \
  -e KYLAS_API_KEY=your-key \
  -e SPRING_DATASOURCE_URL=************************************************* \
  mcp-server
```

## Free Hosting Deployment Options

### 1. Railway (Recommended)

1. Create account at [Railway.app](https://railway.app)
2. Connect your GitHub repository
3. Add environment variables in Railway dashboard:
   - `KYLAS_API_KEY`
   - `SPRING_DATASOURCE_URL` (Railway provides PostgreSQL)
4. Deploy automatically from GitHub

### 2. Render

1. Create account at [Render.com](https://render.com)
2. Create new Web Service from GitHub repo
3. Set build command: `./mvnw clean package -DskipTests`
4. Set start command: `java -jar target/*.jar`
5. Add environment variables in Render dashboard

### 3. Heroku

1. Install Heroku CLI
2. Create Heroku app:
   ```bash
   heroku create your-mcp-server
   heroku addons:create heroku-postgresql:mini
   heroku config:set KYLAS_API_KEY=your-key
   git push heroku main
   ```

### 4. Google Cloud Run

1. Build and push to Google Container Registry:
   ```bash
   gcloud builds submit --tag gcr.io/PROJECT-ID/mcp-server
   ```
2. Deploy to Cloud Run:
   ```bash
   gcloud run deploy --image gcr.io/PROJECT-ID/mcp-server --platform managed
   ```

## Testing the Deployment

Once deployed, test your MCP server:

1. **Health Check:**
   ```bash
   curl https://your-app-url/health
   ```

2. **MCP Endpoint:**
   ```bash
   curl https://your-app-url/mcp
   ```

## Environment Variables for Production

```bash
KYLAS_API_KEY=your-actual-kylas-api-key
SPRING_DATASOURCE_URL=your-database-url
SPRING_DATASOURCE_USERNAME=your-db-username
SPRING_DATASOURCE_PASSWORD=your-db-password
SPRING_PROFILES_ACTIVE=prod
```

## Security Notes

- The application is configured for OAuth2 Resource Server (optional)
- Set `OAUTH2_ISSUER_URI` if using OAuth2 authentication
- Use HTTPS in production
- Keep your Kylas API key secure

## Troubleshooting

1. **Database Connection Issues**: Ensure PostgreSQL is running and credentials are correct
2. **Kylas API Issues**: Verify your API key and network connectivity
3. **Port Conflicts**: Change `server.port` in application.properties if needed

## API Documentation

- Health: `GET /health`
- Root: `GET /`
- MCP Endpoint: `GET /mcp` (Server-Sent Events)
