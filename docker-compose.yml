version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: mcp-postgres
    environment:
      POSTGRES_DB: mcpdb
      POSTGRES_USER: mcpuser
      POSTGRES_PASSWORD: mcppass
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - mcp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U mcpuser -d mcpdb"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Optional: PgAdmin for database management
  # pgadmin:
  #   image: dpage/pgadmin4:latest
  #   container_name: mcp-pgadmin
  #   environment:
  #     PGADMIN_DEFAULT_EMAIL: <EMAIL>
  #     PGADMIN_DEFAULT_PASSWORD: admin123
  #     PGADMIN_CONFIG_SERVER_MODE: 'False'
  #   ports:
  #     - "8081:80"
  #   volumes:
  #     - pgadmin_data:/var/lib/pgadmin
  #   networks:
  #     - mcp-network
  #   restart: unless-stopped
  #   depends_on:
  #     postgres:
  #       condition: service_healthy

volumes:
  postgres_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  mcp-network:
    driver: bridge
